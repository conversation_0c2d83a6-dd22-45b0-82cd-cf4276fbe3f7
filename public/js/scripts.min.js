$.ajaxSetup({
    headers: {
        "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
    },
});

function debounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => {
            func.apply(this, args);
        }, timeout);
    };
}

$(".article-feed").infiniteScroll({
    path: ".pagination__next",
    append: ".article",
    status: ".scroller-status",
    hideNav: ".pagination",
});

(function ($) {
    $(".cart-store").on("click", function (e) {
        e.preventDefault();
        const $this = $(this);
        $.post($this.attr("href")).then((res) => {
            console.log(res);
            $("#cart-count").html(res.count);
            $("#cart-modal").html(res.html);
            $(".cart").show();
            window.scrollTo({ top: 0, behavior: "smooth" });
        });
    });

    $(".scroll-up").click(function (e) {
        e.preventDefault();
        $("html, body").animate(
            { scrollTop: $("#choose").position().top },
            "slow"
        );
    });

    $(document).on("click", ".show-cart", function (e) {
        e.preventDefault();
        $(".cart").toggle();
    });

    const qtyChange = debounce((e) => {
        const $this = $(e.target);
        $.post($this.attr("data-route"), {
            qty: $this.val(),
            _method: "PUT",
        }).then((res) => {
            //console.log("input.qt-input[data-route='"+$this.attr('data-route')+"']")
            // Calculate VAT (20% of total price)
            const vat = (res.total * 0.2).toFixed(2);
            $(".cart-vat-price").html("£" + vat);
            $(".cart-total-price").html("£" + res.total);
            // Calculate Subtotal (total price + VAT)
            const subtotal = (res.total * 1.2).toFixed(2); // This is totalPrice * 1.20
            $(".cart-subtotal-price").html("£" + subtotal);
            // Update hidden input fields with updated values
            $("#total-price").val(res.total);
            $("#vat-price").val(vat);
            $("#subtotal-price").val(subtotal);

            $(
                "input.qt-input[data-route='" + $this.attr("data-route") + "']"
            ).val($this.val());
            if (!$this.hasClass("no-popup")) {
                $(".cart").show();
            }
        });
    });

    // $(".all-courses__btn").each(function (e) {
    //     var href = $(this).attr("href");
    //     var hash = href.substr(href.indexOf("#"));
    //     var id = hash.split('#').pop();
    //     $("#courses").hide();
    //
    //     var timer;
    //     function debounce(){
    //         // clearTimeout(timer);
    //         // timer = setTimeout(function(){
    //         //     $("#courses").fadeOut('fast');
    //         // },450);
    //     }
    //
    //     let div = $("#courses").data("id");
    //
    //     $(this).hover(function() {
    //         if(id = div) {
    //         $("#courses").show();
    //         clearTimeout(timer);
    //     }},function(){
    //         // hover out
    //         debounce();
    //     })
    //     })
    //
    //     $(".course-categoris").mouseenter(function(){
    //         clearTimeout(timer);
    //     });
    //     $(".course-categoris").mouseleave(function(){
    //         debounce();
    //     });
    //
    // $(".all-courses__btn").click(function(e){
    //     $("#courses").show();
    //     e.stopPropagation();
    // });

    $("#icon").click(function (e) {
        $(".menu-popup").toggleClass("active");
        e.stopPropagation();
    });

    $("#courses").click(function (e) {
        e.stopPropagation();
    });
    $(".menu-popup").click(function (e) {
        e.stopPropagation();
    });

    $(document).click(function () {
        $("#courses").hide();
        $(".menu-popup").removeClass("active");
        $("body").removeClass("right-fixed");
        $("#icon").removeClass("open");
    });

    $(".close-it").click(function (e) {
        $("#courses").hide();
    });

    $(document).on("change", "input.qt-input", qtyChange);


    // Cart-Destroy
    $(document).on("click", ".cart-destroy", function (e) {
        e.preventDefault();
        
        const $this = $(this); 
        const url = $this.attr("href");
    
        $.post(url, {
            _method: "DELETE",
        }).then((res) => {
            if (res.success) {
                $this.closest('.c').fadeOut(300, function() {
                    $(this).remove();
                });
    
                $("#cart-count").text(res.count);
    
                $("#sum").text('£' + res.new_total);
    
                if (res.count === 0) {
                  
                }
    
            } else {
                alert(res.message || 'Could not remove item.');
            }
        }).catch(err => {
            console.error('Deletion failed:', err);
            alert('An error occurred. Please try again.');
        });
    });
    // $("#go-checkout").on('click', function (e){
    //     e.preventDefault();
    //     if($('#checkout').hasClass('active')){
    //         console.log("HERE")
    //         $("#checkout-form").submit();
    //         return;
    //     }
    //     $(".tab-pane").removeClass('active').removeClass('show');
    //     $('#payInvoice').removeClass('d-none');
    //     $('#checkout').addClass('active show');
    //     $("#btn-text").text('Payment');
    //     $("#checkout-head").addClass('active')
    // })

    // Add this inside your DOMContentLoaded event listener
    $("#payInvoice").on("click", function (e) {
        e.preventDefault();

        // Validate required fields
        const form = document.getElementById("checkout-form");
        const inputs = form.querySelectorAll(
            'input[data-validation="required"], select[data-validation="required"]'
        );
        let hasEmpty = false;

        inputs.forEach(function (input) {
            if (!input.value.trim()) {
                input.style.border = "2px solid red";
                hasEmpty = true;
            } else {
                input.style.border = "";
            }
        });

        if (hasEmpty) {
            alert("Please fill all required fields.");
            return;
        }

        // Set payment method to invoice
        const $form = $("#checkout-form");
        $form.append(
            '<input name="payment_method" value="invoice" type="hidden"/>'
        );

        // Get address components
        const address1 = $form.find('[name="address1"]').val();
        const address2 = $form.find('[name="address2"]').val();
        const city = $form.find('[name="city"]').val();
        const zip_code = $form.find('[name="zip_code"]').val();
        const country = $form.find('[name="country"]').val();

        // Combine address fields
        const fullAddress = `${address1}${
            address2 ? ", " + address2 : ""
        }, ${city}, ${zip_code}, ${country}`;

        // Append additional invoice-specific data
        $form.append(
            `<input name="address" value="${fullAddress}" type="hidden"/>`
        );
        $form.append(
            `<input name="name_surname" value="${$form
                .find('[name="name"]')
                .val()}" type="hidden"/>`
        );
        $form.append(
            `<input name="job_title" value="${$form
                .find('[name="job"]')
                .val()}" type="hidden"/>`
        );
        $form.append(
            `<input name="email" value="${$form
                .find('[name="email"]')
                .val()}" type="hidden"/>`
        );
        $form.append(
            `<input name="phone" value="${$form
                .find('[name="mobile"]')
                .val()}" type="hidden"/>`
        );
        $form.append(
            `<input name="company" value="${$form
                .find('[name="company"]')
                .val()}" type="hidden"/>`
        );

        // Show loading state
        $(this).prop("disabled", true).find("span").text("Processing...");

        // Submit the form
        $.ajax({
            url: $form.attr("action"),
            type: "POST",
            data: $form.serialize(),
            success: function (response) {
                console.log("Invoice Success", response);
                window.location.href = response.redirect_url;
            },
            error: function (xhr) {
                console.log("Invoice Error", xhr.responseText);
                alert("Invoice request failed. Please try again.");
            },
            complete: function () {
                $("#payInvoice")
                    .prop("disabled", false)
                    .find("span")
                    .text("Pay By Invoice");
            },
        });
    });

    $(".register_c").on("click", function (e) {
        e.preventDefault();
        $("#register_c").modal("show");
        $("#conference-form").attr("action", $(this).attr("href"));
    });

    $(".register_c").on("click", function (e) {
        e.preventDefault();
        $("#register_c").modal("show");
        $("#festival-form").attr("action", $(this).attr("href"));
    });
        $(".register_c").on("click", function (e) {
        e.preventDefault();
        $("#register_c").modal("show");
        $("#awward-form").attr("action", $(this).attr("href"));
    });

    $("#promocode").on("change", function () {
        if ($(this).is(":checked")) {
            $("#promo-input").removeClass("d-none");
        } else {
            $("#promo-input").addClass("d-none");
        }
    });

    jQuery(document).ready(function ($) {
        let autoPlayDelay = 2500;

        let options = {
            init: true,
            // Optional parameters
            loop: false,

            autoplay: {
                delay: autoPlayDelay,
            },

            // If we need pagination
            /*pagination: {
                el: '.swiper-pagination',
                type: 'progressbar'
            },*/

            // Navigation arrows
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
        };

        let mySwiper = new Swiper("#hero", options);

        let slidersCount = mySwiper.params.loop
            ? mySwiper.slides.length - 2
            : mySwiper.slides.length;
        let widthParts = 100 / slidersCount;

        $(".swiper-counter .total").html(slidersCount);
        for (let i = 0; i < slidersCount; i++) {
            $(".swiper-progress-bar .progress-sections").append(
                "<span></span>"
            );
        }

        function initProgressBar() {
            let calcProgress =
                (slidersCount - 1) * (autoPlayDelay + mySwiper.params.speed);
            calcProgress += autoPlayDelay;
            $(".swiper-progress-bar .progress").animate(
                {
                    width: "100%",
                },
                calcProgress,
                "linear"
            );
        }

        initProgressBar();

        mySwiper.on("slideChange", function () {
            let progress = $(".swiper-progress-bar .progress");

            $(".swiper-counter .current").html(this.activeIndex + 1);

            if (
                (this.progress == -0 ||
                    (this.progress == 1 && this.params.loop)) &&
                !progress.parent().is(".stopped")
            ) {
                progress.css("width", "0");
                if (this.activeIndex == 0) {
                    initProgressBar();
                }
            }

            if (progress.parent().is(".stopped")) {
                progress.animate(
                    {
                        width:
                            Math.round(widthParts * (this.activeIndex + 1)) +
                            "%",
                    },
                    this.params.speed,
                    "linear"
                );
            }
        });

        mySwiper.on("touchMove", function () {
            $(".swiper-progress-bar .progress")
                .stop()
                .parent()
                .addClass("stopped");
        });
    });

    if ($().validate) {
        var nShow = false;
        $.formUtils.addValidator({
            name: "even",
            validatorFunction: function (value, $el, config, language, $form) {
                console.log($el.attr("data-validation-name"));
                var radio = $(
                    'input:radio[name="' +
                        $el.attr("data-validation-name") +
                        '"]:checked'
                );
                if (radio.length) {
                    return true;
                }
                $(
                    'input:radio[name="' +
                        $el.attr("data-validation-name") +
                        '"]'
                ).each(function () {
                    $this = $(this);
                    $('label[for="' + $this.attr("id") + '"]').addClass(
                        "error"
                    );
                });
                $(".headerAlert").html(
                    `<div class='alert alert-danger'>${langs[lang].size}</div>`
                );
                return false; //parseInt(value, 10) % 2 === 0;
            },
            errorMessage: "You have to load-coloranswer an even number",
            errorMessageKey: "badEvenNumber",
        });

        $.validate({
            form: ".valid-form",
            errorMessagePosition: "bottom",
            submitErrorMessageCallback() {
                return null;
            },
            inlineErrorMessageCallback() {
                return null;
            },
            ignore: [],
            validateHiddenInputs: true,
        });
    }

    $(".nano").nanoScroller();

    $(".header-menu__nav  a").each(function () {
        if (this.href == window.location.href) {
            $(this).parent().addClass("active");
            $(this).parent().parent().parent().addClass("active");
            $(this)
                .parent()
                .parent()
                .parent()
                .parent()
                .parent()
                .addClass("active");
        }
    });

    $(".blog-slider__item").each(function () {
        var btn1 = $(this).parents(".blog").find(".s-next").attr("id");
        var btn2 = $(this).parents(".blog").find(".s-prev").attr("id");

        console.log(btn1);
        new Swiper("#" + $(this).attr("id"), {
            preloadImages: false,
            // Enable lazy loading
            lazy: true,
            navigation: {
                nextEl: "." + btn1,
                prevEl: "." + btn2,
            },

            autoplay: {
                delay: 6500,
                disableOnInteraction: false,
            },
            slidesPerView: 3,
            spaceBetween: 10,
            spaceBetween: 30,
            breakpoints: {
                640: {
                    slidesPerView: 1,
                    spaceBetween: 30,
                },
                768: {
                    slidesPerView: 2,
                    spaceBetween: 30,
                },
                1024: {
                    slidesPerView: 2,
                    spaceBetween: 30,
                },
            },
        });
    });

    var list = document.getElementsByClassName("bg-img");

    for (var i = 0; i < list.length; i++) {
        var src = list[i].getAttribute("data-img");
        list[i].style.backgroundImage = "url('" + src + "')";
    }

    var list = document.getElementsByClassName("bg-img2");

    for (var i = 0; i < list.length; i++) {
        var src = list[i].getAttribute("data-img");
        list[i].style.backgroundImage = "url('" + src + "')";
    }

    $(".color-bg").each(function (e) {
        let color = $(this).data("bg");
        $(this).css("background-color", color);
    });

    // $("input").on("input.highlight", function() {
    //     // Determine specified search term
    //     var searchTerm = $(this).val();
    //     // Highlight search term inside a specific context
    //     $("#context").unmark().mark(searchTerm);
    // }).trigger("input.highlight").focus();

    $(function () {
        var $context = $("#context");
        var $form = $("form");
        var $button = $form.find("button[name='perform']");
        var $input = $form.find(".highlight");

        $button.on("click.perform", function () {
            // Determine search term
            var searchTerm = $input.val();

            // Determine options
            var options = {};
            var values = $form.serializeArray();
            /* Because serializeArray() ignores unset checkboxes */
            values = values.concat(
                $form
                    .find("input[type='checkbox']:not(:checked)")
                    .map(function () {
                        return {
                            name: this.name,
                            value: "false",
                        };
                    })
                    .get()
            );
            $.each(values, function (i, opt) {
                var key = opt.name;
                var val = opt.value;
                if (key === "keyword" || !val) {
                    return;
                }
                if (val === "false") {
                    val = false;
                } else if (val === "true") {
                    val = true;
                }
                options[key] = val;
            });

            // Remove old highlights and highlight
            // new search term afterwards
            $context.unmark();
            $context.mark(searchTerm, options);
        });
        $button.trigger("click.perform");
    });

    var swiper = new Swiper(".courses-slider", {
        pagination: {
            el: ".swiper-pag",
            clickable: true,
        },
        autoplay: {
            delay: 6500,
            disableOnInteraction: false,
        },
        navigation: {
            nextEl: ".s-p",
            prevEl: ".s-n",
        },
        slidesPerView: 3,
        spaceBetween: 10,
        spaceBetween: 30,
        breakpoints: {
            640: {
                slidesPerView: 1,
                spaceBetween: 30,
            },
            768: {
                slidesPerView: 2,
                spaceBetween: 30,
            },
            1024: {
                slidesPerView: 2,
                spaceBetween: 30,
            },
        },
    });

    var swiper = new Swiper(".related-slider", {
        pagination: {
            el: ".swiper-pagination1",
            clickable: true,
        },
        autoplay: {
            delay: 6500,
            disableOnInteraction: false,
        },
        navigation: {
            nextEl: ".s-p",
            prevEl: ".s-n",
        },
        slidesPerView: 'auto',
        spaceBetween: 10,
        spaceBetween: 30,
        breakpoints: {
            640: {
                slidesPerView: 1,
                spaceBetween: 30,
            },
            768: {
                slidesPerView: 2,
                spaceBetween: 30,
            },
            1024: {
                slidesPerView: 2,
                spaceBetween: 30,
            },
        },
    });

    var swiper = new Swiper(".testimonials-slider", {
        pagination: {
            el: ".swiper-pagination1",
            clickable: true,
        },
        autoplay: {
            delay: 6500,
            disableOnInteraction: false,
        },
        slidesPerView: 3,
        spaceBetween: 10,
        spaceBetween: 30,
        breakpoints: {
            640: {
                slidesPerView: 1,
                spaceBetween: 30,
            },
            768: {
                slidesPerView: 2,
                spaceBetween: 30,
            },
            1024: {
                slidesPerView: 2,
                spaceBetween: 30,
            },
        },
    });

    function marquee() {
        const targets = document.querySelectorAll(".js-marquee");

        if (!targets) return;

        targets.forEach((el) => {
            const tl = gsap.timeline({
                scrollTrigger: {
                    trigger: el,
                    start: "top bottom",
                    scrub: 1,
                    // markers: true,
                },
            });

            tl.to(".js-first", { duration: 4, xPercent: -80 }).to(
                ".js-second",
                { duration: 4, xPercent: 80 },
                "<"
            );
        });
    }

    marquee();

    $(".blog-slider__item2").each(function () {
        var btn1 = $(this).parents(".blog").find(".s-next").attr("id");
        var btn2 = $(this).parents(".blog").find(".s-prev").attr("id");

        console.log(btn1);
        new Swiper("#" + $(this).attr("id"), {
            preloadImages: false,
            // Enable lazy loading
            lazy: true,
            navigation: {
                nextEl: "." + btn1,
                prevEl: "." + btn2,
            },

            autoplay: {
                delay: 6500,
                disableOnInteraction: false,
            },
            slidesPerView: 2,
            spaceBetween: 10,
            spaceBetween: 30,
            breakpoints: {
                640: {
                    slidesPerView: 1,
                    spaceBetween: 30,
                },
                768: {
                    slidesPerView: 2,
                    spaceBetween: 30,
                },
                1024: {
                    slidesPerView: 2,
                    spaceBetween: 30,
                },
            },
        });
    });
    $(".new-item__slider-2").each(function () {
        var btn1 = $(this).parents(".new").find(".s-next").attr("id");
        var btn2 = $(this).parents(".new").find(".s-prev").attr("id");

        console.log(btn1);
        new Swiper("#" + $(this).attr("id"), {
            preloadImages: false,
            // Enable lazy loading
            lazy: true,
            navigation: {
                nextEl: "." + btn1,
                prevEl: "." + btn2,
            },

            autoplay: {
                delay: 6500,
                disableOnInteraction: false,
            },
            slidesPerView: 5,
            spaceBetween: 10,
            spaceBetween: 30,
            breakpoints: {
                640: {
                    slidesPerView: 1,
                    spaceBetween: 30,
                },
                768: {
                    slidesPerView: 2,
                    spaceBetween: 30,
                },
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 30,
                },
            },
        });
    });
    $(".blog-slider").each(function () {
        var btn1 = $(this).parents(".blog").find(".s-next").attr("id");
        var btn2 = $(this).parents(".blog").find(".s-prev").attr("id");

        console.log(btn1);
        new Swiper("#" + $(this).attr("id"), {
            preloadImages: false,
            // Enable lazy loading
            lazy: true,
            navigation: {
                nextEl: "." + btn1,
                prevEl: "." + btn2,
            },

            autoplay: {
                delay: 6500,
                disableOnInteraction: false,
            },
            slidesPerView: 3,
            spaceBetween: 10,
            spaceBetween: 30,
            breakpoints: {
                640: {
                    slidesPerView: 1,
                    spaceBetween: 30,
                },
                768: {
                    slidesPerView: 2,
                    spaceBetween: 30,
                },
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 30,
                },
            },
        });
    });

    $(".offers-slider").each(function () {
        var btn1 = $(this).parents(".f").find(".s-next").attr("id");
        var btn2 = $(this).parents(".f").find(".s-prev").attr("id");

        console.log(btn1);
        new Swiper("#" + $(this).attr("id"), {
            preloadImages: false,
            // Enable lazy loading
            lazy: true,
            navigation: {
                nextEl: "." + btn1,
                prevEl: "." + btn2,
            },

            autoplay: {
                delay: 6500,
                disableOnInteraction: false,
            },
            slidesPerView: 6,
            spaceBetween: 10,
            spaceBetween: 30,
            breakpoints: {
                640: {
                    slidesPerView: 1,
                    spaceBetween: 30,
                },
                768: {
                    slidesPerView: 2,
                    spaceBetween: 30,
                },
                1024: {
                    slidesPerView: 4,
                    spaceBetween: 30,
                },
            },
        });
    });

    $(".offers-slider-2").each(function () {
        var btn1 = $(this).parents(".f").find(".s-next").attr("id");
        var btn2 = $(this).parents(".f").find(".s-prev").attr("id");

        console.log(btn1);
        new Swiper("#" + $(this).attr("id"), {
            preloadImages: false,
            // Enable lazy loading
            lazy: true,
            navigation: {
                nextEl: "." + btn1,
                prevEl: "." + btn2,
            },

            autoplay: {
                delay: 6500,
                disableOnInteraction: false,
            },
            slidesPerView: 3,
            spaceBetween: 10,
            spaceBetween: 30,
            breakpoints: {
                640: {
                    slidesPerView: 1,
                    spaceBetween: 30,
                },
                768: {
                    slidesPerView: 2,
                    spaceBetween: 30,
                },
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 30,
                },
            },
        });
    });

    $(".services-menu ul li a").on("click", function (e) {
        e.preventDefault();

        var id = $(this).attr("data-id");

        $(this).toggleClass("active");

        $(".services-blocks__item").each(function () {
            $(this).removeClass("show");
            if ($(this).attr("id") == id) {
                $(this).addClass("show");
            } else {
                $(this).removeClass("show");
            }
        });
    });

    var swiper = new Swiper(".partners-slider__carousel", {
        slidesPerView: 5,
        spaceBetween: 10,
        pagination: {
            el: ".swiper-pagination",
            clickable: true,
        },

        autoplay: {
            delay: 2500,
            disableOnInteraction: false,
        },
        breakpoints: {
            640: {
                slidesPerView: 2,
                spaceBetween: 20,
            },
            768: {
                slidesPerView: 4,
                spaceBetween: 40,
            },
            1024: {
                slidesPerView: 5,
                spaceBetween: 50,
            },
        },
    });

    var swiper = new Swiper("#slider", {
        slidesPerView: 5,
        spaceBetween: 10,
        pagination: {
            el: ".swiper-pagination",
            clickable: true,
        },
        navigation: {
            nextEl: ".snext",
            prevEl: ".sprev",
        },
        // autoplay: {
        //     delay: 2500,
        //     disableOnInteraction: false,
        // },
        breakpoints: {
            640: {
                slidesPerView: 1.5,
                spaceBetween: 20,
            },
            768: {
                slidesPerView: 4,
                spaceBetween: 40,
            },
            1024: {
                slidesPerView: 5,
                spaceBetween: 50,
            },
        },
    });

    var swiper = new Swiper("#choose", {
        slidesPerView: 4,
        spaceBetween: 10,
        pagination: {
            el: ".swiper-pagination",
            clickable: true,
        },
        navigation: {
            nextEl: ".snext2",
            prevEl: ".sprev2",
        },
        // autoplay: {
        //     delay: 2500,
        //     disableOnInteraction: false,
        // },
        breakpoints: {
            640: {
                slidesPerView: 1.3,
                spaceBetween: 20,
            },
            768: {
                slidesPerView: 4,
                spaceBetween: 40,
            },
            1024: {
                slidesPerView: 4,
                spaceBetween: 50,
            },
        },
    });

    $(document).ready(function () {
        $("#icon, #icon2, #icon3").on("click touchstart", function () {
            $(this).toggleClass("open");
            // $(".menu-popup").toggleClass("active");
            // $("body").toggleClass("right-fixed");
        });
        $(".clo").on("click touchstart", function () {
            $(".menu-popup").removeClass("active");
            $("#icon").removeClass("open");
            $("#courses").hide();
        });
    });

    $(".show-filter").click(function (e) {
        $(".courses-filters").toggle();
    });

    $("#my-modal").on("show.bs.modal", function (event) {
        var title = $(event.relatedTarget).data("title");
        var img = $(event.relatedTarget).data("img");
        var small = $(event.relatedTarget).data("small");
        var text = $(event.relatedTarget).data("text");
        var position = $(event.relatedTarget).data("position");
        var company = $(event.relatedTarget).data("company");

        $(this).find("#exampleModalLabel").text(title);
        $(this).find("#title").text(company);
        $(this).find("#img").attr("src", img);
        $(this).find("#small").text(small);
        $(this).find("#text").html(text);
        $(this).find("#position").text(position);
    });

    // return $(".modal").on("show.bs.modal", function() {
    //     var curModal;
    //     curModal = this;
    //     $(".modal").each(function() {
    //         if (this !== curModal) {
    //             $(this).modal("hide");
    //         }
    //     });
    // });

    let less = $(".load-less-sizes").hide();

    $(".load-more-sizes").click(function (e) {
        less.hide();
        e.preventDefault();
        $(this).parents(".content-this").children("ul").addClass("showsizes");
        $(this).hide();
        less.show();
    });

    $(".load-less-sizes").click(function (e) {
        e.preventDefault();
        $(this)
            .parents(".content-this")
            .children("ul")
            .removeClass("showsizes");
        $(this).hide();
        $(".load-more-sizes").show();
    });

    var timing = 300;

    $(".slider-range2").each(function (e) {
        var $el = $(this);
        var min = $el.parent().find("input.min");
        var max = $el.parent().find("input.max");
        $el.slider({
            range: true,
            min: 0,
            max: 500,
            values: [min.val() || 0, max.val() || 1000000],
            slide: function (event, ui) {
                min.val(ui.values[0]);
                max.val(ui.values[1]);
            },
        });
    });

    $(".min").change(function () {
        $(".slider-range2").slider("values", 0, $(this).val());
        if (!$(".max").val()) {
            $(".max").val(500);
        }
    });
    $(".max").change(function () {
        $(".slider-range2").slider("values", 1, $(this).val());
        if (!$(".min").val()) {
            $(".min").val(0);
        }
    });

    var header = $(".header");
    $(window).scroll(function () {
        var scroll = $(window).scrollTop();

        if (scroll >= 200) {
            header.addClass("fixed");
        } else {
            header.removeClass("fixed");
        }
    });

    $(".show-next").on("click", function (e) {
        e.preventDefault();

        let $this = $(this);
        let parent = $this.parents(".faq-list__item");

        if (parent.hasClass("active")) {
            parent.removeClass("active");
        } else {
            parent.addClass("active");
        }
    });

    $("#upload_link").on("click", function (e) {
        e.preventDefault();
        var uploader = $("#upload:hidden");
        uploader.trigger("click");
    });

    $("#upload:hidden").change(function () {
        let file = $(this)[0].files[0];
        $("#avatar").attr("src", URL.createObjectURL(file));
    });

    $(document).on("click", "div.number > .minus , .plus", function (e) {
        e.preventDefault();
        var $el = $(this);
        var $inp = $el.parent().find("input");
        if ($el.hasClass("plus")) {
            $inp.val(parseInt($inp.val()) + 1);
        } else {
            if (parseInt($inp.val()) - 1 > 1) {
                $inp.val(parseInt($inp.val()) - 1);
            } else {
                $inp.val(1);
            }
        }
        $inp.trigger("change");
    });

    $(window).scroll(startCounter);

    function startCounter() {
        let scrollY =
            (window.pageYOffset || document.documentElement.scrollTop) +
            window.innerHeight;
        let divPos = document.querySelector("#stats").offsetTop;

        if (scrollY > divPos) {
            $(window).off("scroll", startCounter);

            $(".number").each(function () {
                var $this = $(this);
                jQuery({
                    Counter: 0,
                }).animate(
                    {
                        Counter: $this.text().replace(/,/g, ""),
                    },
                    {
                        duration: 4000,
                        easing: "swing",
                        step: function () {
                            $this.text(
                                commaSeparateNumber(Math.floor(this.Counter))
                            );
                        },
                        complete: function () {
                            $this.text(commaSeparateNumber(this.Counter));
                            //alert('finished');
                        },
                    }
                );
            });

            function commaSeparateNumber(num) {
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            }
        }
    }

    var countDownDate = new Date($(".countdown").attr("datetime"));

    function ctd() {
        var now = new Date().getTime();
        var distance = countDownDate - now;
        var days = Math.floor(distance / (1000 * 60 * 60 * 24));
        var hours = Math.floor(
            (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
        );
        var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        var seconds = Math.floor((distance % (1000 * 60)) / 1000);
        countdown = document.getElementsByClassName("countdown");
        if (countdown.length) {
            countdown[0].innerHTML = `
                <div class='item-c'> <strong> ${days}</strong> <span>Days</span></div>
                <div class='item-c'><strong> ${hours}</strong> <span>Hours</span></div>
                <div class='item-c'> <strong>${minutes}</strong> <span>Minutes</span></div>
                <div class='item-c'> <strong>${seconds}</strong> <span>seconds</span></div>

            `;
            if (distance < 0) {
                clearInterval(x);
                countdown.innerHTML = "Item expired!";
            }
        }
    }
    ctd();
    var x = setInterval(ctd, 1000);

    var timerMenu;

    function menuDebounce() {
        clearTimeout(timerMenu);
        timerMenu = setTimeout(function () {
            if (!$("#courses:hover").length) $("#courses").fadeOut("fast");
        }, 450);
    }

    $(".all-courses__btn").on("mouseenter", function () {
        clearTimeout(timerMenu);
        $("#courses").fadeIn("fast");
    });

    $(".all-courses__btn, #courses").on("mouseleave", function () {
        menuDebounce();
    });

    $(".search_it").click(function (e) {
        $(".main-search").toggle();
    });

    $(".filter-input").on("change", function (e) {
        const $this = $(this);
        $this.parents("form").submit();
    });

    $(".clear-filters").on("click", function (e) {
        e.preventDefault();
        const $form = $(this).parents("form");

        // Clear all select inputs
        $form.find("select").each(function() {
            $(this).val("");
        });

        // Submit the form to clear filters
        $form.submit();
    });
})(jQuery);
