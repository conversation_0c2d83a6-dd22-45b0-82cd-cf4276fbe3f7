<?php

namespace App\Http\Controllers;

use App\Models\Location;
use App\Models\Client;
use App\Models\Festival;
use App\Models\FestivalCategory;
use App\Models\FestivalPackage;
use Illuminate\Http\Request;
use DB;

class FestivalController extends Controller
{
    public function index(Request $request)
    {
        $categories = FestivalCategory::withCount('festivals')->get();

        $clients = Client::all();

        $locations = Location::all();

        // Build dynamic list of years from upcoming packages
        $years = FestivalPackage::whereNotNull('start_at')
            ->where('start_at', '>', now())
            ->selectRaw('YEAR(start_at) as year')
            ->distinct()
            ->orderBy('year')
            ->pluck('year');

    //    $data = FestivalPackage::whereIn('festival_id', function ($q){
    //        $q->select('id')->from('festivals');
    //    })->whereNotNull('start_at')->whereNotNull('end_at')
    //        ->with('festival.category', 'festival.location');
//
//        if(!empty($request->category_id)){
//            $data = $data->whereIn('festival_id', function ($q) use ($request) {
//                $q->select('festivals.id')->from('festivals')->where('category_id', $request->category_id);
//            });
//        }
//
//        if(!empty($request->location_id)){
//            $data = $data->where('location_id', $request->location_id);
//        }
//
//        if(!empty($request->year)){
//            $data = $data->where(DB::raw('YEAR(start_at)'), $request->year);
//        }
//
//        if(!empty($request->month)){
//            $data = $data->where(DB::raw('MONTH(start_at)'), $request->year);
//        }
//
//        $data = $data->orderBy('start_at', 'asc')->paginate(8);
        $data = Festival::query()
            ->whereIn('festivals.id', function ($q){
                $q->select('festival_id')->from('festival_packages')->whereNotNull('start_at')->whereNotNull('end_at');
            })->whereHas('packages', function ($q) {
                $q->where('start_at', '>', now());
            });

        if(!empty($request->category_id)) {
            $data = $data->where('category_id', $request->category_id);
        }
        if(!empty($request->location_id)){
           $data = $data->where('location_id', $request->location_id);
        }
        if(!empty($request->year)){
             $data = $data->whereHas('packages', function ($q) use ($request) {
                $q->whereYear('start_at', $request->year);
            });
        }

        if(!empty($request->month)){
            $raw = trim($request->month);
            $normalized = str_replace('.', '', $raw);
            $month = is_numeric($normalized)
                ? (int)$normalized
                : date('n', strtotime("1 {$normalized}"));
             $data = $data->whereHas('packages', function($q) use ($month) {
                $q->whereMonth('start_at', $month);
            });
        }

        // Subquery to get earliest upcoming package per festival
        $firstUpcoming = FestivalPackage::selectRaw('festival_id, MIN(start_at) as first_start_at')
            ->where('start_at', '>', now())
            ->whereNotNull('start_at')
            ->whereNotNull('end_at')
            ->groupBy('festival_id');

        $data = $data
            ->leftJoinSub($firstUpcoming, 'first_packages', function ($join) {
                $join->on('festivals.id', '=', 'first_packages.festival_id');
            })
            ->select('festivals.*', 'first_packages.first_start_at as package_start_at')
            ->with('category', 'location', 'upComing')
            ->orderBy('first_packages.first_start_at', 'asc')
            ->paginate(8);
        $title = __("common.festivals");
        return view('festival.index', compact('categories', 'data', 'clients', 'locations', 'title', 'years'));
    }

        public function show(Request $request)
        {
            $title = preg_replace('/[^a-z0-9]/', '', $request->festival_title);
            $data = Festival::where(DB::raw("LOWER(REGEXP_REPLACE(title, '[^0-9a-z]', ''))"), $title)
                ->with(['packages.location', 'location'])
                ->first();

            // If not found, redirect to homepage
            if (!$data) {
                return redirect('/');
            }

            $upcomingPackages = $data->packages()
                ->where('start_at', '>', now())
                ->get();
            $clients = Client::all();
            $categories = FestivalCategory::all();
            $title = $data->title;

            // Determine if this is UK location for VAT calculation
            $isUKLocation = false;
            if ($upcomingPackages && $upcomingPackages->count() > 0) {
                $firstPackage = $upcomingPackages->first();
                $isUKLocation = optional($firstPackage->location)->id == 1 ||
                               (optional($data->location)->id == 1 && !$firstPackage->location);
            } else {
                // If no packages, check festival location
                $isUKLocation = optional($data->location)->id == 1;
            }

            return view('festival.show', compact('data', 'upcomingPackages', 'clients', 'categories', 'title', 'isUKLocation'));
        }
}
