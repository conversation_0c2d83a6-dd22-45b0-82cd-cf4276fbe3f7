<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\ConferenceCategory;
use App\Models\Location;
use App\Models\Client;
use App\Models\Course;
use App\Models\Conference;
use App\Models\ConferencePackage;
use Illuminate\Http\Request;
use DB;

class ConferenceController extends Controller
{
    public function index(Request $request)
    {
        $categories = ConferenceCategory::withCount('conferences')->get();

        $clients = Client::all();

        $locations = Location::all();

        // Build dynamic list of years from upcoming packages
        $years = ConferencePackage::whereNotNull('start_at')
            ->where('start_at', '>', now())
            ->selectRaw('YEAR(start_at) as year')
            ->distinct()
            ->orderBy('year')
            ->pluck('year');

//        $conferences = ConferencePackage::whereIn('conference_id', function ($q){
//            $q->select('id')->from('conferences');
//        })->whereNotNull('start_at')->whereNotNull('end_at')
//            ->with('conference.category', 'conference.location');
//
//        if(!empty($request->category_id)){
//            $conferences = $conferences->whereIn('conference_id', function ($q) use ($request) {
//                $q->select('conferences.id')->from('conferences')->where('category_id', $request->category_id);
//            });
//        }
//
//        if(!empty($request->location_id)){
//            $conferences = $conferences->where('location_id', $request->location_id);
//        }
//
//        if(!empty($request->year)){
//            $conferences = $conferences->where(DB::raw('YEAR(start_at)'), $request->year);
//        }
//
//        if(!empty($request->month)){
//            $conferences = $conferences->where(DB::raw('MONTH(start_at)'), $request->year);
//        }
//
//        $conferences = $conferences->orderBy('start_at', 'asc')->paginate(8);
        $conferences = Conference::query()
            ->whereIn('conferences.id', function ($q){
                $q->select('conference_id')->from('conference_packages')->whereNotNull('start_at')->whereNotNull('end_at');
            })->whereHas('packages', function ($q) {
                $q->where('start_at', '>', now());
            });

        if(!empty($request->category_id)) {
            $conferences = $conferences->where('category_id', $request->category_id);
        }
        if(!empty($request->location_id)){
           $conferences = $conferences->where('location_id', $request->location_id);
        }
        if(!empty($request->year)){
             $conferences = $conferences->whereHas('packages', function ($q) use ($request) {
                $q->whereYear('start_at', $request->year);
            });
        }

        if(!empty($request->month)){
            $raw = trim($request->month);
            $normalized = str_replace('.', '', $raw);
            $month = is_numeric($normalized)
                ? (int)$normalized
                : date('n', strtotime("1 {$normalized}"));
             $conferences = $conferences->whereHas('packages', function($q) use ($month) {
                $q->whereMonth('start_at', $month);
            });
        }

        // Subquery to get earliest upcoming package per conference
        $firstUpcoming = ConferencePackage::selectRaw('conference_id, MIN(start_at) as first_start_at')
            ->where('start_at', '>', now())
            ->whereNotNull('start_at')
            ->whereNotNull('end_at')
            ->groupBy('conference_id');

        $conferences = $conferences
            ->leftJoinSub($firstUpcoming, 'first_packages', function ($join) {
                $join->on('conferences.id', '=', 'first_packages.conference_id');
            })
            ->select('conferences.*', 'first_packages.first_start_at as package_start_at')
            ->with('category', 'location', 'upComing', 'packages')
            ->orderBy('first_packages.first_start_at', 'asc')
            ->paginate(8);
        // dd($conferences->toArray());
        $title = __("common.conferences");
        return view('conference.index', compact('categories', 'conferences', 'clients', 'locations', 'title', 'years'));
    }

    public function show(Request $request)
    {
        $title = preg_replace('/[^a-z0-9]/', '', $request->conference_title);
        $conference = Conference::where(DB::raw("LOWER(REGEXP_REPLACE(title, '[^0-9a-z]', ''))"), $title)
            ->with(['packages.location', 'location'])
            ->first();

        // If not found, redirect to homepage
        if (!$conference) {
            return redirect('/');
        }

        $upcomingPackages = $conference->packages()
            ->where('start_at', '>', now())
            ->get();

        $clients = Client::all();
        $categories = ConferenceCategory::all();
        $title = $conference->title;

        return view('conference.show', compact('conference','clients', 'upcomingPackages', 'categories', 'title'));
    }

}
