<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Location;
use App\Models\Course;
use App\Models\Client;
use App\Models\CourseLocation;
use Illuminate\Http\Request;
use DB;

class CourseController extends Controller
{
    public function index(Request $request)
    {
        $categories = Category::query()->onlyWithCourse()->withCount('courses')->get();

        $clients = Client::all();

        $locations = Location::all();

        // Build dynamic list of years from upcoming course locations
        $years = CourseLocation::whereNotNull('start_at')
            ->where('start_at', '>', now())
            ->selectRaw('YEAR(start_at) as year')
            ->distinct()
            ->orderBy('year')
            ->pluck('year');

        $courses = CourseLocation::whereIn('course_id', function ($q){
            $q->select('id')->from('courses');
        })->with('course.category', 'location');

        if(!empty($request->category_id)){
            $courses = $courses->whereIn('course_id', function ($q) use ($request) {
                $q->select('courses.id')->from('courses')->where('category_id', $request->category_id);
            });
        }

        if(!empty($request->location_id)){
            $courses = $courses->where('location_id', $request->location_id);
        }

        if(!empty($request->year)){
            $courses = $courses->where(DB::raw('YEAR(start_at)'), $request->year);
        }

        if(!empty($request->month)){
            $month = is_numeric($request->month)
                ? $request->month
                : date('n', strtotime("1 {$request->month}"));
            $courses = $courses->where(DB::raw('MONTH(start_at)'), $month);
        }

        $courses = $courses->orderBy('start_at', 'asc')->paginate(100);
        $title = __("common.courses");
        return view('course.index', compact('categories', 'courses', 'clients', 'locations', 'title', 'years'));
    }

    public function show(Request $request)
    {
        $title = preg_replace('/[^a-z0-9]/', '', $request->course_title);

        $course = Course::where(DB::raw("LOWER(REGEXP_REPLACE(title, '[^0-9a-z]', ''))"), $title)
            ->first();

        // If not found, redirect to homepage
        if (!$course) {
            return redirect('/');
        }

        // Get only upcoming locations for this course
        $upcomingLocations = $course->locations()
            ->where('start_at', '>', now())
            ->get();

        $related = CourseLocation::whereIn('course_id', function ($q) use ($course) {
            $q->select('id')->from('courses')->where('category_id', $course->category_id)->where('id', '<>', $course->id);
        })->with('course.category', 'location')->get();

        $clients = Client::all();
        $title = $course->title;

        return view('course.show', compact('course', 'upcomingLocations', 'related', 'clients', 'title'));
    }

}
