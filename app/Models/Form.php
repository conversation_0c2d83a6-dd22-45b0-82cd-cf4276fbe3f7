<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Form extends Model
{
    use HasFactory;

    public $countries = [
        "Afghanistan",
        "Albania",
        "Algeria",
        "Andorra",
        "Angola",
        "Antigua and Barbuda",
        "Argentina",
        "Armenia",
        "Australia",
        "Austria",
        "Azerbaijan",
        "Bahamas",
        "Bahrain",
        "Bangladesh",
        "Barbados",
        "Belarus",
        "Belgium",
        "Belize",
        "Benin",
        "Bhutan",
        "Bolivia",
        "Bosnia and Herzegovina",
        "Botswana",
        "Brazil",
        "Brunei",
        "Bulgaria",
        "Burkina Faso",
        "Burundi",
        "Cabo Verde",
        "Cambodia",
        "Cameroon",
        "Canada",
        "Central African Republic",
        "Chad",
        "Chile",
        "China",
        "Colombia",
        "Comoros",
        "Congo, Democratic Republic of the",
        "Congo, Republic of the",
        "Costa Rica",
        "Cote d'Ivoire",
        "Croatia",
        "Cuba",
        "Cyprus",
        "Czech Republic",
        "Denmark",
        "Djibouti",
        "Dominica",
        "Dominican Republic",
        "Ecuador",
        "Egypt",
        "El Salvador",
        "Equatorial Guinea",
        "Eritrea",
        "Estonia",
        "Eswatini",
        "Ethiopia",
        "Fiji",
        "Finland",
        "France",
        "Gabon",
        "Gambia",
        "Georgia",
        "Germany",
        "Ghana",
        "Greece",
        "Grenada",
        "Guatemala",
        "Guinea",
        "Guinea-Bissau",
        "Guyana",
        "Haiti",
        "Honduras",
        "Hungary",
        "Iceland",
        "India",
        "Indonesia",
        "Iran",
        "Iraq",
        "Ireland",
        "Israel",
        "Italy",
        "Jamaica",
        "Japan",
        "Jordan",
        "Kazakhstan",
        "Kenya",
        "Kiribati",
        "Korea, North",
        "Korea, South",
        "Kosovo",
        "Kuwait",
        "Kyrgyzstan",
        "Laos",
        "Latvia",
        "Lebanon",
        "Lesotho",
        "Liberia",
        "Libya",
        "Liechtenstein",
        "Lithuania",
        "Luxembourg",
        "Madagascar",
        "Malawi",
        "Malaysia",
        "Maldives",
        "Mali",
        "Malta",
        "Marshall Islands",
        "Mauritania",
        "Mauritius",
        "Mexico",
        "Micronesia",
        "Moldova",
        "Monaco",
        "Mongolia",
        "Montenegro",
        "Morocco",
        "Mozambique",
        "Myanmar",
        "Namibia",
        "Nauru",
        "Nepal",
        "Netherlands",
        "New Zealand",
        "Nicaragua",
        "Niger",
        "Nigeria",
        "North Macedonia",
        "Norway",
        "Oman",
        "Pakistan",
        "Palau",
        "Palestine",
        "Panama",
        "Papua New Guinea",
        "Paraguay",
        "Peru",
        "Philippines",
        "Poland",
        "Portugal",
        "Qatar",
        "Romania",
        "Russia",
        "Rwanda",
        "Saint Kitts and Nevis",
        "Saint Lucia",
        "Saint Vincent and the Grenadines",
        "Samoa",
        "San Marino",
        "Sao Tome and Principe",
        "Saudi Arabia",
        "Senegal",
        "Serbia",
        "Seychelles",
        "Sierra Leone",
        "Singapore",
        "Slovakia",
        "Slovenia",
        "Solomon Islands",
        "Somalia",
        "South Africa",
        "South Sudan",
        "Spain",
        "Sri Lanka",
        "Sudan",
        "Suriname",
        "Sweden",
        "Switzerland",
        "Syria",
        "Taiwan",
        "Tajikistan",
        "Tanzania",
        "Thailand",
        "Timor-Leste",
        "Togo",
        "Tonga",
        "Trinidad and Tobago",
        "Tunisia",
        "Turkey",
        "Turkmenistan",
        "Tuvalu",
        "Uganda",
        "Ukraine",
        "United Arab Emirates",
        "United Kingdom",
        "United States",
        "Uruguay",
        "Uzbekistan",
        "Vanuatu",
        "Vatican City",
        "Venezuela",
        "Vietnam",
        "Yemen",
        "Zambia",
        "Zimbabwe"
    ];


    protected $guarded = ['id'];

    protected $fillable = ['name', 'fields', 'pdf'];

    public $casts = [
        'fields' => 'collection'
    ];

    public function render(){
        return $this->fields->map(fn($item) => match($item['type']){
            'input' => '<div class="col-md-12">
                            <div class="loginform-form">
                                <input name="'.$item['label'].'"  class="f-input" placeholder="'.$item['label'].'" data-validation="required">
                            </div>
                        </div>',
            'textarea' => '<div class="col-md-12">
                            <div class="loginform-form">
                                <textarea name="'.$item['label'].'"  class="f-input" placeholder="'.$item['label'].'" data-validation="required"></textarea>
                            </div>
                        </div>',
            'country' => '<div class="col-md-12"><select name=country"" id="" class="select"><option value="">'.__("common.country").'</option>'.collect($this->countries)->map(function($country){
                return '<option>'.$country.'</option>';
            })->implode('').'</select></div>',
            'checkbox' => '<div class="col-md-12 checkboxs">'.
                            collect(explode(',', data_get($item, 'options', '')))->map(function($opt, $key) use ($item) {
                                return '<div class="custom-control custom-checkbox">
                                            <input id="'.$item['label'].'-'.$key.'" type="checkbox" name="' . $item['label'] . '[]"  class="custom-control-input" value="' . $opt . '" data-validation="required" />
                                            <label class="custom-control-label" for="'.$item['label'].'-'.$key.'">'.$opt.'</label>
                                        </div>';
                            })->implode('')
                        .'</div>',
            'radio' => '<div class="col-md-12 radios">'.
                collect(explode(',', data_get($item, 'options', '')))->map(function($opt, $key) use ($item) {
                    return '<div class="custom-control custom-checkbox">
                                            <input id="'.$item['label'].'-'.$key.'" type="radio" name="' . $item['label'] . '[]"  class="custom-control-input" value="' . $opt . '" data-validation="required" />
                                            <label class="custom-control-label" for="'.$item['label'].'-'.$key.'">'.$opt.'</label>
                                        </div>';
                })->implode('')
                .'</div>',
            'select' => '<div class="col-md-12"><select class="select">'.
                collect(explode(',', data_get($item, 'options', '')))->map(function($opt, $key) use ($item) {
                    return '<option>'.$opt . '</option>';
                })->implode('')
                .'</select></div>',
            default => ''
        })->implode('');
    }
}
