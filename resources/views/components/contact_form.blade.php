<section class="contact__area-6 section pt-2">
    <div class="container">
        <div class="row">
            <div class="col-lg-6">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="contact__item">
                            <i class="icofont-google-map"></i>
                            <h4 class="title">Location</h4>
                            <p>{{data_get($data,"a")}}</p>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="contact__item">
                            <i class="icofont-telephone"></i>
                            <h4 class="title">Call us</h4>
                            <p>{{data_get($data,"m")}}<br>{{data_get($data,"m1")}}</p>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="contact__item">
                            <i class="icofont-mail"></i>
                            <h4 class="title">Email</h4>
                            <a href="mailto:{{data_get($data,"email")}}">{{data_get($data,"email")}}</a>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="contact__item">
                            <i class="icofont-facebook"></i>
                            <h4 class="title">Social Media</h4>
                            <ul>
                                @foreach(data_get($data, "b", []) as $item_of_b)
                                    <li><a href="{{data_get($item_of_b,"l")}}"><i class="{{data_get($item_of_b,"icon")}}"></i></a></li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <!-- Map Section (Display Only) -->
                <div class="contact-map-container">
                    <div id="contact-map"
                         data-lat="{{data_get($data,'map-lat', data_get($data,'lat', '41.7151'))}}"
                         data-lng="{{data_get($data,'map-lng', data_get($data,'lng', '44.8271'))}}"
                         style="height: 450px; width: 100%; border-radius: 8px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    console.log('Contact form script loaded');
    let contactMap;
    let contactMarker;

    function initContactMap() {
        console.log('initContactMap called');
        const mapElement = document.getElementById('contact-map');
        if (!mapElement) {
            console.log('Map element not found');
            return;
        }

        // Get coordinates from data attributes
        const lat = parseFloat(mapElement.dataset.lat) || 41.7151; // Tbilisi default
        const lng = parseFloat(mapElement.dataset.lng) || 44.8271;
        console.log('Coordinates:', lat, lng);

        // Initialize map (read-only for display)
        console.log('Creating map...');
        contactMap = new google.maps.Map(mapElement, {
            zoom: lat && lng && mapElement.dataset.lat ? 15 : 10,
            center: { lat: lat, lng: lng },
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false,
            zoomControl: true,
            scrollwheel: true,
            disableDoubleClickZoom: true,
            draggable: true
        });
        console.log('Map created successfully');

        // Create marker if coordinates exist (non-draggable for display only)
        if (mapElement.dataset.lat && mapElement.dataset.lng) {
            contactMarker = new google.maps.Marker({
                position: { lat: lat, lng: lng },
                map: contactMap,
                draggable: false,
                title: 'ღონისძიების ადგილი'
            });
        }
    }

    // Initialize map when Google Maps API is loaded
    if (typeof google !== 'undefined' && google.maps) {
        initContactMap();
    } else {
        // Load Google Maps API if not already loaded
        window.initContactMap = initContactMap;
        if (!document.querySelector('script[src*="maps.googleapis.com"]')) {
            const script = document.createElement('script');
            script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyDlyxKCa4qM1YmGzR8lty4wMQioFdqIcps&callback=initContactMap`;
            script.async = true;
            script.defer = true;
            document.head.appendChild(script);
        }
    }
</script>

<style>
.contact-map-container {
    position: relative;
}

#contact-map {
    border: 1px solid #ddd;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.contact__item {
    margin-bottom: 30px;
}

.contact__item i {
    font-size: 24px;
    color: #007bff;
    margin-bottom: 10px;
}

.contact__item .title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.contact__item p, .contact__item a {
    color: #666;
    line-height: 1.6;
}

.contact__item ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.contact__item ul li {
    display: inline-block;
    margin-right: 10px;
}

.contact__item ul li a {
    display: inline-block;
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    background: #007bff;
    color: white;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.contact__item ul li a:hover {
    background: #0056b3;
    transform: translateY(-2px);
}
</style>