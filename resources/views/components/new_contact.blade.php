<section class="section section-xl ctaa">
    <div class="container">


        <div class="section-title fl-2 mb-5">
            <h1>{{ data_get($data, 't2') }}</h1><span>{{ data_get($data, 't1') }}</span>
            <p>{{ data_get($data, 'text') }}</p>
        </div>
        <div class="row mt-5">
            <div class="col-md-4">
                <div class="pbmit-ihbox pbmit-ihbox-style-7 contact-box">
                    <div class="d-flex pbmit-ihbox-box">
                        <div class="pbmit-ihbox-icon">
                            <div class="pbmit-ihbox-icon-wrapper"><i class="icofont-location-pin"></i></div>
                        </div>
                        <div class="pbmit-ihbox-contents">
                            <h2 class="pbmit-element-title">Our Location&ZeroWidthSpace;&ZeroWidthSpace;</h2>
                            <div class="pbmit-heading-desc">{{ data_get($data, 'a') }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="pbmit-ihbox pbmit-ihbox-style-7 contact-box">
                    <div class="d-flex pbmit-ihbox-box">
                        <div class="pbmit-ihbox-icon">
                            <div class="pbmit-ihbox-icon-wrapper"><i class="icofont-mobile-phone"></i></div>
                        </div>
                        <div class="pbmit-ihbox-contents">
                            <h2 class="pbmit-element-title"> Phone Number &ZeroWidthSpace;&ZeroWidthSpace;</h2>
                            <div class="pbmit-heading-desc">{{ data_get($data, 'n') }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="pbmit-ihbox pbmit-ihbox-style-7 contact-box">
                    <div class="d-flex pbmit-ihbox-box">
                        <div class="pbmit-ihbox-icon">
                            <div class="pbmit-ihbox-icon-wrapper"><i class="icofont-envelope"></i></div>
                        </div>
                        <div class="pbmit-ihbox-contents">
                            <h2 class="pbmit-element-title"> Email Address &ZeroWidthSpace;&ZeroWidthSpace;</h2>
                            <div class="pbmit-heading-desc">
                                <ul>
                                    @foreach (data_get($data, 'm', []) as $item_of_m)
                                        <li><a
                                                href="mailto:{{ data_get($item_of_m, 'm-a') }}">{{ data_get($item_of_m, 'm-a') }}</a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



<section class="section contact-form-bg position-relative">
    <div class="bg-img" data-img="{{ Storage::url(data_get($data, 'bg-img')) }}"></div>
    <div class="container">
        <div class="row">
            <!-- Map Column -->
            @if(data_get($data, 'a'))
            <div class="col-xl-6 col-md-6">
                <div class="contact-map-wrapper">
                    <div class="pbmit-heading-subheading text-white mb-4">
                        <h4 class="pbmit-subtitle">FIND US</h4>
                        <h2 class="pbmit-title">Our Location</h2>
                    </div>
                    <div class="contact-map-container">
                        <div id="contact-map"
                             data-address="{{ data_get($data, 'a') }}"
                             style="height: 400px; width: 100%; border-radius: 8px; margin-bottom: 20px;">
                        </div>
                        <div class="location-info text-white">
                            <!-- <p><strong>Address:</strong><br>{{ data_get($data, 'a') }}</p> -->
                            @if(data_get($data, 'n'))
                            <p><strong>Phone:</strong><br>{{ data_get($data, 'n') }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @else
            <div class="col-xl-6 col-md-5"></div>
            @endif
            <!-- Form Column -->
            <div class="col-xl-6 col-md-{{ data_get($data, 'a') ? '6' : '7' }}">
                <div class="contact-form">
                    <div class="pbmit-heading-subheading text-white">
                        <h4 class="pbmit-subtitle">CONTACT US</h4>
                        <h2 class="pbmit-title">Enquiry Form</h2>
                    </div>
                    @if (session('success'))
                        <div class="alert alert-success mt-3">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if ($errors->any())
                        <div class="alert alert-danger mt-3">
                            <ul>
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    <form method="post" id="contact-form" action="{{ route('contact.send') }}"
                        novalidate="novalidate">
                        @csrf
                        <div class="row">
                            <div class="col-sm-6">
                                <input type="text" name="name" class="form-control" placeholder="Name"
                                    value="{{ old('name') }}" required>
                            </div>

                            <div class="col-sm-6">
                                <input type="email" name="email" class="form-control" placeholder="Email"
                                    value="{{ old('email') }}" required>
                            </div>

                            <div class="col-sm-6">
                                <input type="text" name="number" class="form-control" placeholder="Phone"
                                    value="{{ old('number') }}" required>
                            </div>

                            <div class="col-sm-6">
                                <input type="text" name="subject" class="form-control" placeholder="Subject"
                                    value="{{ old('subject') }}" required>
                            </div>

                            <div class="col-md-12">
                                <textarea name="message" cols="40" rows="5" class="form-control" placeholder="Message" required>{{ old('message') }}</textarea>
                            </div>
                            <div class="col-md-12"><button type="submit" class="pbmit-btn pbmit-btn-lg"><i
                                        class="form-btn-loader fa fa-circle-o-notch fa-spin fa-fw margin-bottom d-none"></i>Send
                                    Message</button></div>
                            <div class="col-md-12 col-lg-12 message-status"></div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-form');
    const messageStatus = document.querySelector('.message-status');
    const submitBtn = contactForm.querySelector('button[type="submit"]');
    const loader = submitBtn.querySelector('.form-btn-loader');
    const originalBtnText = submitBtn.innerHTML;

    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Show loading state
        submitBtn.disabled = true;
        loader.classList.remove('d-none');
        submitBtn.innerHTML = '<i class="form-btn-loader fa fa-circle-o-notch fa-spin fa-fw margin-bottom"></i>Sending...';

        // Clear previous messages
        messageStatus.innerHTML = '';

        // Create FormData
        const formData = new FormData(contactForm);

        // Submit via fetch
        fetch(contactForm.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                messageStatus.innerHTML = '<div class="alert alert-success mt-3">' + data.message + '</div>';
                // Reset form
                contactForm.reset();
            } else {
                // Show error messages
                let errorHtml = '<div class="alert alert-danger mt-3"><ul>';
                if (data.errors) {
                    Object.values(data.errors).forEach(errorArray => {
                        errorArray.forEach(error => {
                            errorHtml += '<li>' + error + '</li>';
                        });
                    });
                } else {
                    errorHtml += '<li>An error occurred. Please try again.</li>';
                }
                errorHtml += '</ul></div>';
                messageStatus.innerHTML = errorHtml;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            messageStatus.innerHTML = '<div class="alert alert-danger mt-3">An error occurred. Please try again.</div>';
        })
        .finally(() => {
            // Restore button state
            submitBtn.disabled = false;
            loader.classList.add('d-none');
            submitBtn.innerHTML = originalBtnText;
        });
    });
});

// Map functionality
let contactMap;
let contactMarker;

function initContactMap() {
    const mapElement = document.getElementById('contact-map');

    if (!mapElement) {
        return;
    }

    // Get address from data attribute
    const address = mapElement.dataset.address;

    if (!address) {
        return;
    }

    // Use Google Geocoding to get coordinates from address
    const geocoder = new google.maps.Geocoder();

    geocoder.geocode({ address: address }, function(results, status) {
        if (status === 'OK') {
            const location = results[0].geometry.location;
            const lat = location.lat();
            const lng = location.lng();

            // Initialize map (read-only for display)
            contactMap = new google.maps.Map(mapElement, {
                zoom: 15,
                center: location,
                mapTypeControl: false,
                streetViewControl: false,
                fullscreenControl: false,
                zoomControl: true,
                scrollwheel: true,
                disableDoubleClickZoom: true,
                draggable: true
            });

            // Create marker (non-draggable for display only)
            contactMarker = new google.maps.Marker({
                position: location,
                map: contactMap,
                draggable: false,
                title: 'Our Location'
            });
        }
    });
}

// Initialize map when Google Maps API is loaded
if (typeof google !== 'undefined' && google.maps) {
    initContactMap();
} else {
    // Load Google Maps API if not already loaded
    window.initContactMap = initContactMap;
    if (!document.querySelector('script[src*="maps.googleapis.com"]')) {
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyDlyxKCa4qM1YmGzR8lty4wMQioFdqIcps&callback=initContactMap`;
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);
    }
}
</script>

<style>
.contact-map-container {
    position: relative;
}

#contact-map {
    border: 1px solid #ddd;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.section-title p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 0;
}
</style>
