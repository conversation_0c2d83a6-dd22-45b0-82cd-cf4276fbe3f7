@extends('app')

@section('content')
    @include('includes.header')
    <section class="section courses">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="section-title">
                        <span>@lang("common.conferences_title_1")</span>
                        <h1>@lang("common.conferences_title_2")</h1>
                        <ul>
                            <li class="sprev"><i class="icofont-rounded-left"></i></li>
                            <li class="snext"><i class="icofont-rounded-right"></i></li>
                        </ul>
                    </div>

                    <div class="courses-slider">
                        <div class="swiper-container courses-slider__slider" id="slider">
                            <div class="swiper-wrapper">
                                @foreach($categories as $cat)
                                    <div class="swiper-slide">
                                        <div class="courses-slider__item">
                                            <a href="?category_id={{$cat->id}}">
                                                <figure>
                                                    <img src="{{Storage::url($cat->image)}}" alt="" />
                                                </figure>
                                                <div class="title-in">
                                                    <h2>{{$cat->title}}</h2>
                                                    <span>{{$cat->conferences_count}} Conferences</span>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>


                <div class="col-md-12 ">
                    <div class="spead">
                        <ul>
                            <li><a href="#"><svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M18.4384 20C19.3561 20 20.1493 19.3726 20.2725 18.4632C20.3895 17.5988 20.5 16.4098 20.5 15C20.5 12 20.6683 10.1684 17.5 7C16.0386 5.53865 14.4064 4.41899 13.3024 3.74088C12.4978 3.24665 11.5021 3.24665 10.6975 3.74088C9.5935 4.41899 7.96131 5.53865 6.49996 7C3.33157 10.1684 3.49997 12 3.49997 15C3.49997 16.4098 3.61039 17.5988 3.72745 18.4631C3.85061 19.3726 4.64378 20 5.56152 20H18.4384Z" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg> @lang("common.home")</a></li>
                            <li class="active">@lang("common.conferences")</li>
                        </ul>
                    </div>
                    <button class="show-filter"><svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 3H5C3.58579 3 2.87868 3 2.43934 3.4122C2 3.8244 2 4.48782 2 5.81466V6.50448C2 7.54232 2 8.06124 2.2596 8.49142C2.5192 8.9216 2.99347 9.18858 3.94202 9.72255L6.85504 11.3624C7.49146 11.7206 7.80967 11.8998 8.03751 12.0976C8.51199 12.5095 8.80408 12.9935 8.93644 13.5872C9 13.8722 9 14.2058 9 14.8729L9 17.5424C9 18.452 9 18.9067 9.25192 19.2613C9.50385 19.6158 9.95128 19.7907 10.8462 20.1406C12.7248 20.875 13.6641 21.2422 14.3321 20.8244C15 20.4066 15 19.4519 15 17.5424V14.8729C15 14.2058 15 13.8722 15.0636 13.5872C15.1959 12.9935 15.488 12.5095 15.9625 12.0976C16.1903 11.8998 16.5085 11.7206 17.145 11.3624L20.058 9.72255C21.0065 9.18858 21.4808 8.9216 21.7404 8.49142C22 8.06124 22 7.54232 22 6.50448V5.81466C22 4.48782 22 3.8244 21.5607 3.4122C21.1213 3 20.4142 3 19 3Z" stroke="#1C274C" stroke-width="1.5"/>
                        </svg>   @lang("common.show_filter")</button>
                    <!-- /.show-filter -->
                    <div class="courses-filters">
                        <div class="filter-block">
                            <div class="filter-block__titletop">
                                <h1>@lang("common.filter_title")</h1>
                                <span><svg width="800px" height="800px" viewBox="0 0 192 192" xmlns="http://www.w3.org/2000/svg" fill="none"><path d="M5.999 60.165V93.79M101.97 80l.005 54.095m-51.128-56.35c-.265 18.528 0 37.687 0 56.35m103.151 0V79.999c-.107-28.355-51.575-28.715-52.024 0 .133-7.346-3.623-14.21-9.867-18.032m5.382-7.212-8.97-8.565 24.667-20.286A222.577 222.577 0 0 0 6 60.164h32.29v15.327c21.841 5.004 42.561 3.44 59.2-20.736z" class="a" style="fill:none;stroke:#000000;stroke-width:12;stroke-linecap:round;stroke-linejoin:round;stroke-dasharray:none" transform="translate(16.001 16)"/></svg>
                                    {{$conferences->total()}} @lang("common.result")</span>
                            </div>
                            <!-- /.filter-block__title -->
                            <form action="" class="filter-form">
                                <div class="collapseFormItem">
                                    <div id="section__gui_body" class="collapseBody collapse show">
                                        <div class="content-this2">
                                            <div class="controlItem form-group">
                                                <div class="filter-block__item">
                                                    <div class="filter-block__title">
                                                        <h1>@lang("common.by_cat")</h1>
                                                    </div>
                                                    <div class="content-this">
                                                        <select name="category_id" id="" class="select filter-input">
                                                            <option value="" {{ request('category_id')=='' ? 'selected' : '' }}>Select Categories</option>
                                                            @foreach($categories as $cat)
                                                                <option value="{{$cat->id}}" {{ (string)request('category_id') === (string)$cat->id ? 'selected' : '' }}>{{$cat->title}}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="content-this2">
                                            <div class="controlItem form-group">
                                                <div class="filter-block__item">
                                                    <div class="filter-block__title">
                                                        <h1>@lang("common.by_year")</h1>
                                                    </div>
                                                    <div class="content-this">
                                                        <select name="year" id="" class="select filter-input">
                                                            <option value="" {{ request('year')=='' ? 'selected' : '' }}>Select Year</option>
                                                            @foreach($years as $y)
                                                                <option value="{{$y}}" {{ (string)request('year') === (string)$y ? 'selected' : '' }}>{{$y}}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="content-this2">
                                            <div class="controlItem form-group">
                                                <div class="filter-block__item">
                                                    <div class="filter-block__title">
                                                        <h1>@lang("common.by_month")</h1>
                                                    </div>
                                                    <div class="content-this">
                                                        <select name="month" id="" class="select filter-input">
                                                            <option value="" {{ request('month')=='' ? 'selected' : '' }}>Select month</option>
                                                            @for ($m=1; $m<=12; $m++)
                                                                @php $mn = date('F', mktime(0,0,0,$m, 1, date('Y'))); @endphp
                                                                <option value="{{$mn}}" {{ request('month') === $mn ? 'selected' : '' }}>{{$mn}}</option>
                                                            @endfor
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div> <div class="content-this2">
                                            <div class="controlItem form-group">
                                                <div class="filter-block__item">
                                                    <div class="filter-block__title">
                                                        <h1>@lang("common.by_location")</h1>
                                                    </div>
                                                    <div class="content-this">
                                                        <select name="location_id" id="" class="select filter-input">
                                                            <option value="" {{ request('location_id')=='' ? 'selected' : '' }}>Select Location</option>
                                                            @foreach($locations as $location)
                                                                <option value="{{$location->id}}" {{ (string)request('location_id') === (string)$location->id ? 'selected' : '' }}>{{$location->name}}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" class="btn-click">@lang("common.search")</button>
                                <button type="button" class="btn-click clear-filters" style="background-color: #6c757d; margin-left: 10px;">Clear</button>
                            </form>
                        </div>
                    </div>

                    <div class="courses-items">
                        <div class="courses-items__list">
                            <div class="row">
                                @if(!$conferences->count())
                                <div class="col-md-12">
                                    <div class="not-found">
                                        <svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" width="790" height="512.20805" viewBox="0 0 790 512.20805" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M925.56335,704.58909,903,636.49819s24.81818,24.81818,24.81818,45.18181l-4.45454-47.09091s12.72727,17.18182,11.45454,43.27273S925.56335,704.58909,925.56335,704.58909Z" transform="translate(-205 -193.89598)" fill="#e6e6e6"/><path d="M441.02093,642.58909,419,576.13509s24.22155,24.22155,24.22155,44.09565l-4.34745-45.95885s12.42131,16.76877,11.17917,42.23245S441.02093,642.58909,441.02093,642.58909Z" transform="translate(-205 -193.89598)" fill="#e6e6e6"/><path d="M784.72555,673.25478c.03773,43.71478-86.66489,30.26818-192.8092,30.35979s-191.53562,13.68671-191.57335-30.028,86.63317-53.29714,192.77748-53.38876S784.68782,629.54,784.72555,673.25478Z" transform="translate(-205 -193.89598)" fill="#e6e6e6"/><rect y="509.69312" width="790" height="2" fill="#3f3d56"/><polygon points="505.336 420.322 491.459 420.322 484.855 366.797 505.336 366.797 505.336 420.322" fill="#a0616a"/><path d="M480.00587,416.35743H508.3101a0,0,0,0,1,0,0V433.208a0,0,0,0,1,0,0H464.69674a0,0,0,0,1,0,0v-1.54149A15.30912,15.30912,0,0,1,480.00587,416.35743Z" fill="#2f2e41"/><polygon points="607.336 499.322 593.459 499.322 586.855 445.797 607.336 445.797 607.336 499.322" fill="#a0616a"/><path d="M582.00587,495.35743H610.3101a0,0,0,0,1,0,0V512.208a0,0,0,0,1,0,0H566.69674a0,0,0,0,1,0,0v-1.54149A15.30912,15.30912,0,0,1,582.00587,495.35743Z" fill="#2f2e41"/><path d="M876.34486,534.205A10.31591,10.31591,0,0,0,873.449,518.654l-32.23009-131.2928L820.6113,396.2276l38.33533,126.949a10.37185,10.37185,0,0,0,17.39823,11.0284Z" transform="translate(-205 -193.89598)" fill="#a0616a"/><path d="M851.20767,268.85955a11.38227,11.38227,0,0,0-17.41522,1.15247l-49.88538,5.72709,7.58861,19.24141,45.36779-8.49083a11.44393,11.44393,0,0,0,14.3442-17.63014Z" transform="translate(-205 -193.89598)" fill="#a0616a"/><path d="M769,520.58909l21.76811,163.37417,27.09338-5.578s-3.98437-118.98157,9.56238-133.32513S810,505.58909,810,505.58909Z" transform="translate(-205 -193.89598)" fill="#2f2e41"/><path d="M778,475.58909l-10,15s-77-31.99929-77,19-4.40631,85.60944-6,88,18.43762,8.59375,28,7c0,0,11.79687-82.21884,11-87,0,0,75.53355,37.03335,89.87712,33.84591S831.60944,536.964,834,530.58909s-1-57-1-57l-47.81-14.59036Z" transform="translate(-205 -193.89598)" fill="#2f2e41"/><path d="M779.34915,385.52862l-2.85032-3.42039s-31.92361-71.82815-19.3822-91.21035,67.26762-22.23252,68.97783-21.0924-4.08488,15.9428-.09446,22.78361c0,0-42.394,9.19121-45.24435,10.33134s21.96615,43.2737,21.96615,43.2737l-2.85031,25.6529Z" transform="translate(-205 -193.89598)" fill="#ccc"/><path d="M835.21549,350.18459S805.57217,353.605,804.432,353.605s-1.71017-7.41084-1.71017-7.41084l-26.223,35.91406S763.57961,486.29929,767,484.58909s66.50531,8.11165,67.07539,3.55114-.57008-27.3631,1.14014-28.50324,29.64328-71.82811,29.64328-71.82811-2.85032-14.82168-12.54142-19.95227S835.21549,350.18459,835.21549,350.18459Z" transform="translate(-205 -193.89598)" fill="#ccc"/><path d="M855.73783,378.11779l9.121,9.69109S878.41081,499.1687,871,502.58909s-22,3-22,3l-14.35458-52.79286Z" transform="translate(-205 -193.89598)" fill="#ccc"/><circle cx="601.72966" cy="122.9976" r="26.2388" fill="#a0616a"/><path d="M800.57267,320.98789c-.35442-5.44445-7.22306-5.631-12.67878-5.68255s-11.97836.14321-15.0654-4.35543c-2.0401-2.973-1.65042-7.10032.035-10.28779s4.45772-5.639,7.18508-7.99742c7.04139-6.08884,14.29842-12.12936,22.7522-16.02662s18.36045-5.472,27.12788-2.3435c10.77008,3.84307,25.32927,23.62588,26.5865,34.99176s-3.28507,22.95252-10.9419,31.44586-25.18188,5.0665-36.21069,8.088c6.7049-9.48964,2.28541-26.73258-8.45572-31.164Z" transform="translate(-205 -193.89598)" fill="#2f2e41"/><circle cx="361.7217" cy="403.5046" r="62.98931" fill="#d02333"/><path d="M524.65625,529.9355a45.15919,45.15919,0,0,1-41.25537-26.78614L383.44873,278.05757a59.83039,59.83039,0,1,1,111.87012-41.86426l72.37744,235.41211a45.07978,45.07978,0,0,1-43.04,58.33008Z" transform="translate(-205 -193.89598)" fill="#d02333"/></svg>
                                        <h3>@lang("common.not_found")</h3>
                                        <a href="/contact-us">@lang("common.contact_us")</a>
                                    </div>
                                </div>
                                @endif
                                @include('includes.conference-list')
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="section partners partners-full">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="section-title">
                        <div class="icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="1284.000000pt" height="1058.000000pt" viewBox="0 0 1284.000000 1058.000000" preserveAspectRatio="xMidYMid meet">
                                <g transform="translate(0.000000,1058.000000) scale(0.100000,-0.100000)"
                                   fill="#000000" stroke="none">
                                    <path d="M5409 8514 c-80 -71 -174 -154 -210 -185 -35 -31 -224 -197 -419
                           -369 -195 -172 -440 -388 -544 -479 -104 -91 -196 -173 -205 -181 -9 -8 -144
                           -127 -301 -265 -157 -137 -328 -287 -380 -334 -52 -46 -108 -96 -125 -110 -16
                           -15 -187 -165 -380 -335 -192 -170 -399 -352 -460 -404 -226 -197 -277 -265
                           -311 -419 -20 -92 -14 -189 17 -281 l21 -63 27 24 c14 13 37 34 51 46 82 74
                           203 140 327 177 78 24 105 27 238 28 223 1 268 -15 570 -198 502 -304 578
                           -348 621 -363 62 -21 164 -13 224 17 25 13 122 90 215 173 94 82 313 275 487
                           428 174 153 345 311 379 351 115 134 199 284 253 458 41 128 72 368 47 358 -4
                           -1 -39 -31 -77 -64 -38 -34 -172 -153 -299 -264 -126 -111 -355 -313 -508
                           -449 -154 -135 -300 -259 -327 -274 -103 -60 -225 -43 -312 44 -29 29 -48 56
                           -46 67 3 14 224 215 503 455 33 29 85 75 115 102 30 28 89 80 130 115 41 36
                           77 67 80 70 3 3 41 37 85 76 44 39 127 112 185 163 58 51 131 123 164 160 166
                           187 278 435 306 678 21 178 54 194 -366 -176 -205 -179 -405 -356 -445 -392
                           -41 -36 -150 -132 -244 -214 -93 -82 -193 -169 -220 -194 -28 -25 -100 -89
                           -160 -141 -61 -52 -128 -111 -149 -130 -57 -51 -54 -49 -146 -130 -47 -41 -87
                           -77 -90 -80 -32 -32 -37 -32 -130 15 -50 25 -130 56 -177 69 l-86 22 124 110
                           c68 60 205 181 304 269 100 88 286 252 415 365 129 113 259 228 290 255 97 87
                           203 181 261 231 230 199 457 404 504 457 159 174 276 423 310 656 9 60 15 126
                           13 146 l-3 37 -146 -128z"/>
                                    <path d="M1563 8149 c13 -196 91 -423 204 -591 72 -107 148 -190 303 -326 385
                           -341 519 -458 624 -551 51 -44 61 -49 75 -38 9 8 57 49 106 92 197 172 211
                           187 186 197 -5 2 -60 48 -122 103 -62 55 -158 140 -213 189 -56 49 -117 103
                           -136 120 -19 17 -163 144 -320 281 -157 137 -290 254 -296 260 -73 67 -399
                           351 -407 354 -7 3 -8 -25 -4 -90z"/>
                                    <path d="M1560 7168 c0 -201 92 -467 228 -663 70 -101 117 -151 265 -282 104
                           -93 120 -103 135 -92 9 8 71 61 137 120 66 58 130 114 143 125 12 10 22 21 22
                           25 0 4 -86 82 -192 175 -204 180 -450 397 -508 448 -19 17 -73 65 -120 106
                           -47 41 -86 78 -88 83 -11 23 -22 0 -22 -45z"/>

                                </g>
                            </svg>

                        </div>
                        <!-- /.icon -->
                        <h1>@lang("common.partners_title_2")</h1>
                    </div>
                    <!-- /.section-title -->
                </div>
                <!-- /.col-md-12 -->
                <div class="col-md-12">
                    <div class="partners-slider">
                        <div class="partners-slider__carousel swiper-container">
                            <div class="swiper-wrapper">
                                @foreach($clients as $client)
                                    <div class="swiper-slide">
                                        <div class="partners-item__carousel-item">
                                            <a href="{{$client->link}}" title="{{$client->name}}">
                                                <figure>
                                                    <img src="{{Storage::url($client->image)}}" alt="{{$client->name}}" />
                                                </figure>
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.col-md-12 -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container -->
    </section>
@stop
